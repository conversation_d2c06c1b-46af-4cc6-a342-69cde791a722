const FnosSign = require('./fnos_sqlite3');
const fs = require('fs');

async function runBasicTest() {
    console.log('=== 飞牛论坛脚本基础测试 ===\n');
    
    try {
        console.log('1. 检查依赖...');
        try {
            require('axios');
            console.log('✅ axios 依赖正常');
        } catch (e) {
            console.log('❌ axios 依赖缺失，请运行: npm install axios');
            return;
        }
        
        try {
            require('sqlite3');
            console.log('✅ sqlite3 依赖正常');
        } catch (e) {
            console.log('❌ sqlite3 依赖缺失，请运行: npm install sqlite3');
            return;
        }
        
        console.log('\n2. 测试脚本初始化...');
        const fnos = new FnosSign({
            task: 'all',
            readTime: [5, 10],
            maxRetries: 1
        });
        console.log('✅ 脚本实例创建成功');
        
        console.log('\n3. 测试数据库初始化...');
        await fnos.waitForDatabase();
        console.log('✅ 数据库初始化成功');
        
        console.log('\n4. 测试基础功能...');
        const postList = await fnos.getPostList();
        console.log(`✅ 帖子列表生成成功 (${postList.tids.length} 个帖子)`);
        
        console.log('\n5. 清理测试数据...');
        await fnos.closeDatabase();
        if (fs.existsSync(fnos.dbPath)) {
            try {
                fs.unlinkSync(fnos.dbPath);
                console.log('✅ 测试数据库已清理');
            } catch (e) {
                console.log('⚠️  测试数据库清理失败（文件可能被占用）');
            }
        }
        
        console.log('\n🎉 基础测试通过！脚本可以正常使用。');
        console.log('\n📋 下一步操作:');
        console.log('   1. 在 fnos_sqlite3.js 中配置您的 Cookie');
        console.log('   2. 运行: node fnos_sqlite3.js');
        console.log('   3. 查看 README.md 了解更多使用方法');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        console.log('\n🔧 请检查:');
        console.log('   - Node.js 版本是否 >= 14.0');
        console.log('   - 是否正确安装了依赖');
        console.log('   - 是否有文件读写权限');
    }
}

if (require.main === module) {
    runBasicTest();
}

module.exports = { runBasicTest };
