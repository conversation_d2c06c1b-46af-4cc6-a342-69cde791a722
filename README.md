# 飞牛论坛自动签到脚本

一个功能完整的飞牛论坛（club.fnnas.com）自动签到和浏览点赞脚本，支持普通签到、抢签到、浏览点赞等功能，并使用 SQLite 数据库记录操作历史。

## 功能特性

- ✅ **自动签到**：支持普通签到和抢签到模式
- ✅ **浏览点赞**：自动浏览帖子并点赞，避免重复点赞
- ✅ **数据记录**：使用 SQLite 数据库记录所有操作历史
- ✅ **智能重试**：网络异常时自动重试
- ✅ **时间控制**：浏览任务间隔控制，避免频繁操作
- ✅ **多种模式**：支持仅签到、仅浏览、全部执行等模式
- ✅ **用户信息**：获取并显示用户详细信息和排名

## 环境要求

- Node.js 14.0 或更高版本
- npm 包管理器

## 快速开始

### 1. 安装依赖

```bash
# 使用 npm
npm install

# 或者手动安装
npm install axios sqlite3
```

### 2. 配置 Cookie

在 `fnos_sqlite3.js` 文件的第 1051 行，将 `cookieString` 替换为您的实际 Cookie：

```javascript
const cookieString = "您的Cookie字符串";
```

### 3. 运行脚本

```bash
# 执行所有任务
npm start
# 或
node fnos_sqlite3.js

# 仅签到
npm run sign

# 仅浏览点赞
npm run browse

# 抢签到模式
npm run rush
```

## 配置说明

### 1. 获取 Cookie

1. 打开浏览器，登录飞牛论坛
2. 按 F12 打开开发者工具
3. 访问签到页面：`https://club.fnnas.com/plugin.php?id=zqlj_sign`
4. 在 Network 标签页找到请求，复制 Cookie 值
5. 将 Cookie 值替换到代码中的 `cookieString` 变量

### 2. 修改配置

在代码中找到配置部分并根据需要修改：

```javascript
const fnos = new FnosSign({
    task: 'all',              // 执行任务类型
    readTime: [15, 30],       // 阅读时间范围（秒）
    postCount: 1,             // 浏览帖子数量
    maxRetries: 3,            // 最大重试次数
    // 抢签到配置
    concurrentRequests: 80,   // 并发请求数量
    signRequestTimeout: 300,  // 请求超时时间
    signAttemptDuration: 8000 // 抢签到尝试时长
});
```

## 使用方法

### 基本用法

```bash
# 执行所有任务（签到 + 浏览点赞）
node fnos_sqlite3.js

# 仅执行签到
node fnos_sqlite3.js --sign

# 仅执行浏览点赞
node fnos_sqlite3.js --browse

# 抢签到模式（适合0点抢签到）
node fnos_sqlite3.js --rush
```

### 任务模式说明

| 参数 | 说明 | 适用场景 |
|------|------|----------|
| 无参数 | 执行所有任务 | 日常使用 |
| `--sign` | 仅执行签到 | 只需要签到 |
| `--browse` | 仅执行浏览点赞 | 只需要浏览任务 |
| `--rush` | 抢签到模式 | 0点抢签到 |

### 定时任务设置

#### Windows 任务计划程序

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器（如每天早上8点）
4. 设置操作：
   - 程序：`node`
   - 参数：`fnos_sqlite3.js`
   - 起始位置：脚本所在目录

#### Linux/macOS Crontab

```bash
# 编辑 crontab
crontab -e

# 添加定时任务（每天8点执行）
0 8 * * * cd /path/to/script && node fnos_sqlite3.js

# 抢签到任务（每天0点执行）
0 0 * * * cd /path/to/script && node fnos_sqlite3.js --rush
```

## 数据库说明

脚本会自动创建 `fnos.db` SQLite 数据库文件，包含以下表：

### sign_records（签到记录）
- `id`：记录ID
- `sign_time`：签到时间
- `success`：是否成功
- `reward`：获得飞牛币数量
- `rank`：签到排名
- `network_delay`：网络延迟
- `execution_time`：执行耗时
- `message`：签到结果消息

### browse_records（浏览记录）
- `id`：记录ID
- `browse_time`：浏览时间
- `post_id`：帖子ID
- `success`：是否成功
- `read_time`：阅读时间
- `like_success`：点赞是否成功
- `message`：结果消息

### liked_posts（已点赞帖子）
- `id`：记录ID
- `post_id`：帖子ID（唯一）
- `like_time`：点赞时间
- `title`：帖子标题
- `author`：帖子作者

### user_stats（用户状态）
- `id`：记录ID
- `check_time`：检查时间
- `username`：用户名
- `coins`：飞牛币数量
- `credits`：积分
- `login_days`：登录天数
- `sign_days`：累计签到天数
- `continuous_days`：连续签到天数
- `sign_level`：签到等级

## 注意事项

1. **Cookie 安全**：请妥善保管您的 Cookie，不要泄露给他人
2. **频率控制**：浏览任务有10分钟间隔限制，避免频繁操作
3. **网络环境**：建议在稳定的网络环境下运行
4. **抢签到时机**：抢签到模式仅在每天23:50-23:59之间有效
5. **数据备份**：定期备份 `fnos.db` 数据库文件

## 故障排除

### 常见问题

1. **签到失败**
   - 检查 Cookie 是否过期
   - 检查网络连接
   - 查看错误日志

2. **数据库错误**
   - 确保有写入权限
   - 检查磁盘空间
   - 删除损坏的 `fnos.db` 文件重新运行

3. **浏览任务跳过**
   - 检查是否在10分钟间隔内
   - 确认有可点赞的帖子

4. **依赖安装失败**
   ```bash
   # 清理缓存重新安装
   npm cache clean --force
   npm install

   # 或使用 yarn
   yarn install
   ```

### 日志查看

脚本运行时会输出详细日志，包括：
- 连接状态
- 签到结果
- 浏览统计
- 用户信息
- 错误信息

### 测试脚本

运行测试确保功能正常：

```bash
npm test
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本签到和浏览功能
- 添加 SQLite 数据库支持
- 实现抢签到模式

## 许可证

本项目仅供学习交流使用，请遵守相关网站的使用条款。

## 维护者

- alxxxxla

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。
